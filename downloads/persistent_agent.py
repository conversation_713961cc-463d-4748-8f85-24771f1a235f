#!/usr/bin/env python3
# Linux Persistent Agent for Wegweiser
# Based on Windows persistent_agent.py

import os
import sys
import time
import json
import signal
import logging
import threading
import subprocess
from datetime import datetime
from pathlib import Path

try:
    import socketio
except ImportError:
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'python-socketio[client]'])
    import socketio

try:
    import requests
except ImportError:
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'requests'])
    import requests

try:
    from logzero import logger, logfile
except ImportError:
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'logzero'])
    from logzero import logger, logfile


class PersistentAgent:
    def __init__(self):
        # Configuration paths
        self.root_dir = Path("/opt/Wegweiser")
        self.config_dir = self.root_dir / "Config"
        self.log_dir = self.root_dir / "Logs"
        self.scripts_dir = self.root_dir / "Scripts"
        self.persistent_dir = self.root_dir / "PersistentAgent"
        
        # Ensure directories exist
        for directory in [self.config_dir, self.log_dir, self.persistent_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        log_file = self.log_dir / "persistent_agent.log"
        logfile(str(log_file), maxBytes=10*1024*1024, backupCount=5)
        
        # Configuration
        self.config_file = self.config_dir / "persistent_config.json"
        self.pid_file = self.persistent_dir / "persistent_agent.pid"
        self.status_file = self.persistent_dir / "status.json"
        
        # SocketIO client
        self.sio = socketio.Client(
            reconnection=True,
            reconnection_attempts=0,  # Infinite attempts
            reconnection_delay=1,
            reconnection_delay_max=30,
            randomization_factor=0.5
        )
        
        # Runtime state
        self.is_running = False
        self.device_uuid = None
        self.group_uuid = None
        self.server_url = "https://app.wegweiser.tech"
        self.heartbeat_interval = 60  # seconds
        self.last_heartbeat = None
        
        # Setup signal handlers
        signal.signal(signal.SIGTERM, self._signal_handler)
        signal.signal(signal.SIGINT, self._signal_handler)
        
        # Setup SocketIO event handlers
        self._setup_socketio_handlers()
        
        logger.info("Persistent Agent initialized")

    def _setup_socketio_handlers(self):
        """Setup SocketIO event handlers"""
        
        @self.sio.event
        def connect():
            logger.info("Connected to Wegweiser server")
            self._update_status("connected")
            if self.device_uuid:
                self.sio.emit('join_device_room', {'device_uuid': self.device_uuid})
        
        @self.sio.event
        def disconnect():
            logger.warning("Disconnected from Wegweiser server")
            self._update_status("disconnected")
        
        @self.sio.event
        def connect_error(data):
            logger.error(f"Connection error: {data}")
            self._update_status("error", str(data))
        
        @self.sio.on('execute_command')
        def handle_execute_command(data):
            """Handle command execution requests from server"""
            try:
                command = data.get('command')
                command_id = data.get('command_id')
                logger.info(f"Received command execution request: {command}")
                
                # Execute the standard agent to handle the command
                result = self._execute_agent_command(command)
                
                # Send result back to server
                self.sio.emit('command_result', {
                    'command_id': command_id,
                    'device_uuid': self.device_uuid,
                    'result': result
                })
                
            except Exception as e:
                logger.error(f"Error handling command execution: {e}")
        
        @self.sio.on('update_agent')
        def handle_update_agent(data):
            """Handle agent update requests"""
            try:
                logger.info("Received agent update request")
                self._trigger_agent_update()
            except Exception as e:
                logger.error(f"Error handling agent update: {e}")

    def load_config(self):
        """Load configuration from file"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.device_uuid = config.get('device_uuid')
                    self.group_uuid = config.get('group_uuid')
                    self.server_url = config.get('server_url', self.server_url)
                    self.heartbeat_interval = config.get('heartbeat_interval', self.heartbeat_interval)
                    logger.info(f"Loaded config: device_uuid={self.device_uuid}")
                    return True
            else:
                logger.warning("No persistent config found")
                return False
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return False

    def save_config(self):
        """Save configuration to file"""
        try:
            config = {
                'device_uuid': self.device_uuid,
                'group_uuid': self.group_uuid,
                'server_url': self.server_url,
                'heartbeat_interval': self.heartbeat_interval,
                'last_update': datetime.now().isoformat()
            }
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=2)
            logger.info("Configuration saved")
        except Exception as e:
            logger.error(f"Error saving config: {e}")

    def _get_device_uuid_from_standard_agent(self):
        """Get device UUID from standard agent configuration"""
        try:
            # Try to get device UUID from standard agent's config
            standard_config = self.config_dir / "weg.dat"
            if standard_config.exists():
                with open(standard_config, 'r') as f:
                    data = json.load(f)
                    return data.get('deviceuuid')
        except Exception as e:
            logger.warning(f"Could not read standard agent config: {e}")
        return None

    def _execute_agent_command(self, command=None):
        """Execute the standard agent"""
        try:
            python_path = self.root_dir / "Agent" / "python-weg" / "bin" / "python3"
            agent_script = self.scripts_dir / "agent.py"
            
            cmd = [str(python_path), str(agent_script)]
            if command:
                cmd.extend(command.split())
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            return {
                'returncode': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'success': result.returncode == 0
            }
            
        except subprocess.TimeoutExpired:
            logger.error("Agent command timed out")
            return {'success': False, 'error': 'Command timed out'}
        except Exception as e:
            logger.error(f"Error executing agent command: {e}")
            return {'success': False, 'error': str(e)}

    def _trigger_agent_update(self):
        """Trigger the standard agent update process"""
        try:
            python_path = self.root_dir / "Agent" / "python-weg" / "bin" / "python3"
            update_script = self.root_dir / "Scripts" / "updateAgent.py"
            
            if update_script.exists():
                subprocess.Popen([str(python_path), str(update_script)])
                logger.info("Triggered agent update")
            else:
                logger.warning("Update script not found")
        except Exception as e:
            logger.error(f"Error triggering agent update: {e}")

    def _update_status(self, status, message=None):
        """Update status file"""
        try:
            status_data = {
                'status': status,
                'timestamp': datetime.now().isoformat(),
                'device_uuid': self.device_uuid,
                'pid': os.getpid()
            }
            if message:
                status_data['message'] = message
            
            with open(self.status_file, 'w') as f:
                json.dump(status_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error updating status: {e}")

    def _heartbeat_loop(self):
        """Heartbeat loop to maintain connection"""
        while self.is_running:
            try:
                if self.sio.connected and self.device_uuid:
                    self.sio.emit('heartbeat', {
                        'device_uuid': self.device_uuid,
                        'timestamp': datetime.now().isoformat(),
                        'agent_type': 'persistent'
                    })
                    self.last_heartbeat = datetime.now()
                    logger.debug("Heartbeat sent")
                
                time.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                time.sleep(10)  # Wait before retrying

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.stop()

    def _write_pid(self):
        """Write PID to file"""
        try:
            with open(self.pid_file, 'w') as f:
                f.write(str(os.getpid()))
        except Exception as e:
            logger.error(f"Error writing PID file: {e}")

    def _remove_pid(self):
        """Remove PID file"""
        try:
            if self.pid_file.exists():
                self.pid_file.unlink()
        except Exception as e:
            logger.error(f"Error removing PID file: {e}")

    def start(self):
        """Start the persistent agent"""
        try:
            logger.info("Starting Wegweiser Persistent Agent")
            
            # Write PID file
            self._write_pid()
            
            # Load configuration
            if not self.load_config():
                # Try to get device UUID from standard agent
                self.device_uuid = self._get_device_uuid_from_standard_agent()
                if self.device_uuid:
                    self.save_config()
                else:
                    logger.error("No device UUID found. Ensure standard agent is registered first.")
                    return False
            
            if not self.device_uuid:
                logger.error("No device UUID available")
                return False
            
            self.is_running = True
            self._update_status("starting")
            
            # Start heartbeat thread
            heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            heartbeat_thread.start()
            
            # Connect to server
            socket_url = f"{self.server_url}"
            logger.info(f"Connecting to {socket_url}")
            
            try:
                self.sio.connect(socket_url, wait_timeout=30)
                logger.info("Successfully connected to server")
                self._update_status("running")
                
                # Keep the main thread alive
                while self.is_running:
                    time.sleep(1)
                    
            except Exception as e:
                logger.error(f"Failed to connect to server: {e}")
                self._update_status("error", str(e))
                return False
                
        except Exception as e:
            logger.error(f"Error starting persistent agent: {e}")
            self._update_status("error", str(e))
            return False
        finally:
            self._remove_pid()

    def stop(self):
        """Stop the persistent agent"""
        logger.info("Stopping persistent agent")
        self.is_running = False
        
        if self.sio.connected:
            self.sio.disconnect()
        
        self._update_status("stopped")
        self._remove_pid()

    def status(self):
        """Get agent status"""
        try:
            if self.status_file.exists():
                with open(self.status_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Error reading status: {e}")
        return {"status": "unknown"}


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Wegweiser Persistent Agent')
    parser.add_argument('action', choices=['start', 'stop', 'status', 'restart'], 
                       help='Action to perform')
    parser.add_argument('-d', '--daemon', action='store_true', 
                       help='Run as daemon (background process)')
    
    args = parser.parse_args()
    
    agent = PersistentAgent()
    
    if args.action == 'start':
        if args.daemon:
            # Fork to background
            try:
                pid = os.fork()
                if pid > 0:
                    print(f"Started persistent agent as daemon (PID: {pid})")
                    sys.exit(0)
            except OSError as e:
                print(f"Fork failed: {e}")
                sys.exit(1)
            
            # Redirect standard file descriptors
            with open('/dev/null', 'r') as stdin:
                os.dup2(stdin.fileno(), sys.stdin.fileno())
            with open('/dev/null', 'w') as stdout:
                os.dup2(stdout.fileno(), sys.stdout.fileno())
            with open('/dev/null', 'w') as stderr:
                os.dup2(stderr.fileno(), sys.stderr.fileno())
        
        agent.start()
    
    elif args.action == 'stop':
        # Read PID and send TERM signal
        try:
            if agent.pid_file.exists():
                with open(agent.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                os.kill(pid, signal.SIGTERM)
                print(f"Sent stop signal to process {pid}")
            else:
                print("PID file not found. Agent may not be running.")
        except Exception as e:
            print(f"Error stopping agent: {e}")
    
    elif args.action == 'status':
        status = agent.status()
        print(json.dumps(status, indent=2))
    
    elif args.action == 'restart':
        # Stop then start
        try:
            if agent.pid_file.exists():
                with open(agent.pid_file, 'r') as f:
                    pid = int(f.read().strip())
                os.kill(pid, signal.SIGTERM)
                time.sleep(2)  # Wait for graceful shutdown
        except:
            pass
        
        if args.daemon:
            try:
                pid = os.fork()
                if pid > 0:
                    print(f"Restarted persistent agent as daemon (PID: {pid})")
                    sys.exit(0)
            except OSError as e:
                print(f"Fork failed: {e}")
                sys.exit(1)
        
        agent.start()


if __name__ == "__main__":
    main()
