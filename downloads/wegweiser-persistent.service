[Unit]
Description=Wegweiser Persistent Agent
Documentation=https://app.wegweiser.tech/docs
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=500
StartLimitBurst=5

[Service]
Type=simple
User=root
Group=root
ExecStart=/opt/Wegweiser/PersistentAgent/python-weg-persistent/bin/python3 /opt/Wegweiser/Scripts/persistent_agent.py start
ExecStop=/opt/Wegweiser/PersistentAgent/python-weg-persistent/bin/python3 /opt/Wegweiser/Scripts/persistent_agent.py stop
Restart=always
RestartSec=30
TimeoutStartSec=60
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/Wegweiser
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

# Environment
Environment=PYTHONPATH=/opt/Wegweiser/Scripts
WorkingDirectory=/opt/Wegweiser

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=wegweiser-persistent

[Install]
WantedBy=multi-user.target
