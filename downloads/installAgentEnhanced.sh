#!/bin/bash

# Enhanced Wegweiser Agent Installation Script
# Supports both cron-based and persistent agents

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

# Usage function
usage() {
    echo "Enhanced Wegweiser Agent Installer"
    echo ""
    echo "Usage: $0 [OPTIONS] <groupuuid>"
    echo ""
    echo "Options:"
    echo "  -h, --help              Show this help message"
    echo "  -c, --cron-only         Install only cron-based agent (default: both)"
    echo "  -p, --persistent-only   Install only persistent agent"
    echo "  -u, --update            Update existing installation"
    echo "  --no-systemd            Skip systemd service installation for persistent agent"
    echo ""
    echo "Arguments:"
    echo "  groupuuid               UUID of the group to register this agent with"
    echo ""
    echo "Examples:"
    echo "  $0 12345678-1234-5678-9abc-123456789abc"
    echo "  $0 -c 12345678-1234-5678-9abc-123456789abc  # Cron only"
    echo "  $0 -p 12345678-1234-5678-9abc-123456789abc  # Persistent only"
    echo "  $0 -u 12345678-1234-5678-9abc-123456789abc  # Update existing"
    exit 1
}

# Default settings
INSTALL_CRON=true
INSTALL_PERSISTENT=true
UPDATE_MODE=false
INSTALL_SYSTEMD=true
GROUPUUID=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            ;;
        -c|--cron-only)
            INSTALL_CRON=true
            INSTALL_PERSISTENT=false
            shift
            ;;
        -p|--persistent-only)
            INSTALL_CRON=false
            INSTALL_PERSISTENT=true
            shift
            ;;
        -u|--update)
            UPDATE_MODE=true
            shift
            ;;
        --no-systemd)
            INSTALL_SYSTEMD=false
            shift
            ;;
        -*)
            error "Unknown option $1"
            usage
            ;;
        *)
            if [ -z "$GROUPUUID" ]; then
                GROUPUUID=$1
            else
                error "Multiple group UUIDs specified"
                usage
            fi
            shift
            ;;
    esac
done

# Validate arguments
if [ -z "$GROUPUUID" ]; then
    error "Group UUID is required"
    usage
fi

# Validate UUID format (basic check)
if ! [[ $GROUPUUID =~ ^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$ ]]; then
    error "Invalid UUID format: $GROUPUUID"
    exit 1
fi

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    error "This script must be run as root (use sudo)"
    exit 1
fi

# Directory structure
ROOTFOLDER="/opt/Wegweiser"
LOGFOLDER="${ROOTFOLDER}/Logs"
CONFIGFOLDER="${ROOTFOLDER}/Config"
AGENTFOLDER="${ROOTFOLDER}/Agent"
PERSISTENTFOLDER="${ROOTFOLDER}/PersistentAgent"
FILESFOLDER="${ROOTFOLDER}/Files"
SCRIPTSFOLDER="${ROOTFOLDER}/Scripts"

# Files for cron agent
CRON_AGENT_FILE="${SCRIPTSFOLDER}/agent.py"
CRON_RUN_FILE="${AGENTFOLDER}/runAgent.sh"
CRON_REQ_FILE="${AGENTFOLDER}/requirements.txt"
CRON_VENV_DIR="${AGENTFOLDER}/python-weg"

# Files for persistent agent
PERSISTENT_AGENT_FILE="${SCRIPTSFOLDER}/persistent_agent.py"
PERSISTENT_REQ_FILE="${PERSISTENTFOLDER}/persistent_requirements.txt"
PERSISTENT_VENV_DIR="${PERSISTENTFOLDER}/python-weg-persistent"
SYSTEMD_SERVICE_FILE="/etc/systemd/system/wegweiser-persistent.service"

# Download URLs
BASE_URL="https://app.wegweiser.tech/download"
CRON_AGENT_URL="${BASE_URL}/agent.py"
CRON_RUN_URL="${BASE_URL}/runAgent.sh"
CRON_REQ_URL="${BASE_URL}/requirements.txt"
PERSISTENT_AGENT_URL="${BASE_URL}/persistent_agent.py"
PERSISTENT_REQ_URL="${BASE_URL}/persistent_requirements.txt"
SYSTEMD_SERVICE_URL="${BASE_URL}/wegweiser-persistent.service"

# Cron job settings
FULLMESSAGE="# Wegweiser Agent (Every Minute)"
CRONJOBFULL="*/1 * * * * sudo sh ${CRON_RUN_FILE}"

install_system_dependencies() {
    log "Installing system dependencies..."
    apt-get update -qq
    apt-get install -y python3-venv python3-pip curl wget systemctl > /dev/null 2>&1
    log "System dependencies installed"
}

create_directories() {
    log "Creating directory structure..."
    
    # Create root folder if it doesn't exist
    if [ ! -d "$ROOTFOLDER" ]; then
        mkdir -p "$ROOTFOLDER"
        log "Created root folder: $ROOTFOLDER"
    fi
    
    # Set restrictive permissions on root folder
    chmod 700 "$ROOTFOLDER"
    
    # Create subdirectories
    for dir in "$LOGFOLDER" "$CONFIGFOLDER" "$FILESFOLDER" "$SCRIPTSFOLDER"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "Created directory: $dir"
        fi
    done
    
    # Create agent-specific directories based on what we're installing
    if [ "$INSTALL_CRON" = true ]; then
        if [ ! -d "$AGENTFOLDER" ]; then
            mkdir -p "$AGENTFOLDER"
            log "Created cron agent directory: $AGENTFOLDER"
        fi
    fi
    
    if [ "$INSTALL_PERSISTENT" = true ]; then
        if [ ! -d "$PERSISTENTFOLDER" ]; then
            mkdir -p "$PERSISTENTFOLDER"
            log "Created persistent agent directory: $PERSISTENTFOLDER"
        fi
    fi
}

create_virtual_environment() {
    local venv_dir=$1
    local req_file=$2
    local agent_type=$3
    
    log "Creating virtual environment for $agent_type agent..."
    
    if [ -d "$venv_dir" ] && [ "$UPDATE_MODE" = false ]; then
        warn "Virtual environment already exists: $venv_dir"
        return 0
    fi
    
    # Remove existing virtual environment if updating
    if [ "$UPDATE_MODE" = true ] && [ -d "$venv_dir" ]; then
        log "Removing existing virtual environment for update..."
        rm -rf "$venv_dir"
    fi
    
    # Create new virtual environment
    python3 -m venv "$venv_dir"
    
    # Install requirements
    if [ -f "$req_file" ]; then
        log "Installing Python packages for $agent_type agent..."
        "$venv_dir/bin/pip3" install --upgrade pip > /dev/null 2>&1
        "$venv_dir/bin/pip3" install -r "$req_file" > /dev/null 2>&1
        log "Python packages installed for $agent_type agent"
    else
        warn "Requirements file not found: $req_file"
    fi
}

download_file() {
    local url=$1
    local dest=$2
    local description=$3
    
    log "Downloading $description..."
    
    # Create backup if file exists and we're updating
    if [ -f "$dest" ] && [ "$UPDATE_MODE" = true ]; then
        cp "$dest" "${dest}.backup.$(date +%Y%m%d_%H%M%S)"
        log "Created backup of existing file"
    fi
    
    if curl -f -s -o "$dest" "$url"; then
        log "Downloaded $description successfully"
        return 0
    else
        error "Failed to download $description from $url"
        return 1
    fi
}

install_cron_agent() {
    if [ "$INSTALL_CRON" = false ]; then
        return 0
    fi
    
    log "Installing cron-based agent..."
    
    # Download files
    download_file "$CRON_AGENT_URL" "$CRON_AGENT_FILE" "cron agent script" || return 1
    download_file "$CRON_RUN_URL" "$CRON_RUN_FILE" "cron run script" || return 1
    download_file "$CRON_REQ_URL" "$CRON_REQ_FILE" "cron requirements" || return 1
    
    # Make run script executable
    chmod +x "$CRON_RUN_FILE"
    
    # Create virtual environment and install packages
    create_virtual_environment "$CRON_VENV_DIR" "$CRON_REQ_FILE" "cron"
    
    # Register agent for the first time (only if not updating)
    if [ "$UPDATE_MODE" = false ]; then
        log "Registering cron agent with group UUID: $GROUPUUID"
        if sudo sh "$CRON_RUN_FILE" -g "$GROUPUUID"; then
            log "Cron agent registered successfully"
        else
            error "Failed to register cron agent"
            return 1
        fi
    fi
    
    # Setup cron job
    setup_cron_job
    
    log "Cron-based agent installation completed"
}

install_persistent_agent() {
    if [ "$INSTALL_PERSISTENT" = false ]; then
        return 0
    fi
    
    log "Installing persistent agent..."
    
    # Download files
    download_file "$PERSISTENT_AGENT_URL" "$PERSISTENT_AGENT_FILE" "persistent agent script" || return 1
    download_file "$PERSISTENT_REQ_URL" "$PERSISTENT_REQ_FILE" "persistent requirements" || return 1
    
    # Make agent script executable
    chmod +x "$PERSISTENT_AGENT_FILE"
    
    # Create virtual environment and install packages
    create_virtual_environment "$PERSISTENT_VENV_DIR" "$PERSISTENT_REQ_FILE" "persistent"
    
    # Install systemd service
    if [ "$INSTALL_SYSTEMD" = true ]; then
        install_systemd_service
    fi
    
    log "Persistent agent installation completed"
}

setup_cron_job() {
    log "Setting up cron job..."
    
    # Remove existing cron entries for this agent
    crontab -l 2>/dev/null | grep -v -F "$FULLMESSAGE" | grep -v -F "$CRON_RUN_FILE" | crontab - 2>/dev/null || true
    
    # Add new cron entries
    (crontab -l 2>/dev/null || echo ""; echo "$FULLMESSAGE"; echo "$CRONJOBFULL") | crontab -
    
    log "Cron job configured successfully"
}

install_systemd_service() {
    log "Installing systemd service..."
    
    # Download service file
    download_file "$SYSTEMD_SERVICE_URL" "$SYSTEMD_SERVICE_FILE" "systemd service file" || {
        warn "Failed to download systemd service file, creating default one..."
        create_default_systemd_service
    }
    
    # Reload systemd
    systemctl daemon-reload
    
    # Enable service
    systemctl enable wegweiser-persistent.service
    
    log "Systemd service installed and enabled"
}

create_default_systemd_service() {
    cat > "$SYSTEMD_SERVICE_FILE" << EOF
[Unit]
Description=Wegweiser Persistent Agent
Documentation=https://app.wegweiser.tech/docs
After=network-online.target
Wants=network-online.target
StartLimitIntervalSec=500
StartLimitBurst=5

[Service]
Type=simple
User=root
Group=root
ExecStart=$PERSISTENT_VENV_DIR/bin/python3 $PERSISTENT_AGENT_FILE start
ExecStop=$PERSISTENT_VENV_DIR/bin/python3 $PERSISTENT_AGENT_FILE stop
Restart=always
RestartSec=30
TimeoutStartSec=60
TimeoutStopSec=30

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/Wegweiser
CapabilityBoundingSet=CAP_NET_BIND_SERVICE

# Environment
Environment=PYTHONPATH=$SCRIPTSFOLDER
WorkingDirectory=$ROOTFOLDER

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=wegweiser-persistent

[Install]
WantedBy=multi-user.target
EOF
}

start_services() {
    if [ "$INSTALL_PERSISTENT" = true ] && [ "$INSTALL_SYSTEMD" = true ]; then
        log "Starting persistent agent service..."
        
        if systemctl start wegweiser-persistent.service; then
            log "Persistent agent service started successfully"
            
            # Wait a moment and check status
            sleep 3
            if systemctl is-active --quiet wegweiser-persistent.service; then
                log "Persistent agent is running correctly"
            else
                warn "Persistent agent service may have issues. Check: systemctl status wegweiser-persistent.service"
            fi
        else
            error "Failed to start persistent agent service"
        fi
    fi
}

print_summary() {
    echo ""
    echo -e "${BLUE}=== Installation Summary ===${NC}"
    echo ""
    
    if [ "$INSTALL_CRON" = true ]; then
        echo -e "${GREEN}✓${NC} Cron-based agent installed"
        echo "  - Agent script: $CRON_AGENT_FILE"
        echo "  - Run script: $CRON_RUN_FILE"
        echo "  - Virtual environment: $CRON_VENV_DIR"
        echo "  - Cron job: Runs every minute"
    fi
    
    if [ "$INSTALL_PERSISTENT" = true ]; then
        echo -e "${GREEN}✓${NC} Persistent agent installed"
        echo "  - Agent script: $PERSISTENT_AGENT_FILE"
        echo "  - Virtual environment: $PERSISTENT_VENV_DIR"
        if [ "$INSTALL_SYSTEMD" = true ]; then
            echo "  - Systemd service: wegweiser-persistent.service"
        fi
    fi
    
    echo ""
    echo "Configuration:"
    echo "  - Root directory: $ROOTFOLDER"
    echo "  - Group UUID: $GROUPUUID"
    echo "  - Logs directory: $LOGFOLDER"
    echo ""
    
    if [ "$INSTALL_PERSISTENT" = true ] && [ "$INSTALL_SYSTEMD" = true ]; then
        echo "Persistent Agent Management:"
        echo "  - Start:   systemctl start wegweiser-persistent"
        echo "  - Stop:    systemctl stop wegweiser-persistent"
        echo "  - Status:  systemctl status wegweiser-persistent"
        echo "  - Logs:    journalctl -u wegweiser-persistent -f"
        echo ""
    fi
    
    echo -e "${GREEN}Installation completed successfully!${NC}"
}

# Main execution
main() {
    log "Starting Wegweiser Agent Installation"
    log "Group UUID: $GROUPUUID"
    log "Install cron agent: $INSTALL_CRON"
    log "Install persistent agent: $INSTALL_PERSISTENT"
    log "Update mode: $UPDATE_MODE"
    
    # Check internet connectivity
    if ! curl -f -s --head "$BASE_URL" > /dev/null; then
        error "Cannot reach Wegweiser server at $BASE_URL"
        error "Please check your internet connection"
        exit 1
    fi
    
    # Install system dependencies
    install_system_dependencies
    
    # Create directory structure
    create_directories
    
    # Install agents based on selection
    install_cron_agent || {
        error "Failed to install cron agent"
        exit 1
    }
    
    install_persistent_agent || {
        error "Failed to install persistent agent"
        exit 1
    }
    
    # Start services
    start_services
    
    # Print summary
    print_summary
}

# Execute main function
main "$@"
