{"settings": {"snippetUuid": "c77bae46-04c2-4544-9a7f-f51041b6c177", "snippetname": "updateAgentWithPersistent", "snippettype": ".py", "created_at": 1749240862}, "payload": {"payloadsig": "BPz26l6kE4+1DjC5uRgGnY1spxSQ/6R/1ZJpZrE02NY4htb/d4GBQeO7PvHBLK/llKuRGd7wZGnhkyVi3DdEIUTOvlic31Rr4q4/wdQtpU5Fuyw1qcIxxPi3Nx7lXjWqTAsV9IeDtYTk6sX/hjMhdBdz6OqAYnRBhgNwx6/MGyono48i368Fov04t3K8ctkC5A5BOxFb/LM7PM973jg2XeAcX9yJUJ2OW9OH4R9BRILn5S/nK9NYhgcnv4URnSZdCqe+PJ01vzy7FWhyngEb2CdD557/Yen0OI3rPu5qmyWbSUyv8+FCD4GsniqP08x4s58MabnKa7v2Tr8S+Lo6xdwDZ8ZWuaQuUoVeqGlwGON4zEGnzRunbhlkSRSOfUmfYryK/BvUcex6EMpCow5gaZ9EoSiu/mfviHgT6Q0+EhHjwlJHrVHJX/mn/JV/QJ97z6ncj0+MUrJVG4/qej/G7rS/pD4xc3nwaZt3Q84VeruXZS84lZaaTCvVRD4nG2vKx2dK3gN0tOZPJ+oHFWMdM4DbPun+UIU6sEDpnm+jJ4oyAgQdLgShgRiyvIPwgMqHeMQ+YKuiT0KWmR8vAvkgVwoztADiyl8/SLVEhTS5SZjKkgg4Ld8DD7Ow7VGZW94X7gTrMmTuXwNp9cXGQDqiFYtXgu4M+3EIq/xctQ4mMlo=", "payloadb64": "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"}}