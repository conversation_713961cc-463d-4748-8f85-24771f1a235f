{"settings": {"snippetUuid": "6f6380f8-0cca-4089-b966-e25e9358bd08", "snippetname": "updateAgentWithPersistent", "snippettype": ".py", "created_at": 1749241554}, "payload": {"payloadsig": "GNxmZm67mwC7Zu4lYd7zvi96yf7bnrkXWRgQrHQuDGXFvh3cpwBJQuPdHOy/HLa9K/8VeUgdL9rsrCV2F61OI+s6Ddlhv7gQjjXgpvcrVovl0FdLX6sJTN9Jkad91fPrjEOKdSfiCHM0w5y53sc7TzT02FlswZcJ8Eixy5xC8ZhrcNjqQkxOXC6xdHyXLvE5ea3BYIja18P8UvByRioOdj0dXzXbfkCSoql8Df/caVbAcCdTIomsI+jZhiol24W5Wt0Q6jDu7FIkRahifdD0a5wTHNLiR9il/QmeB1gaPVPfzXTvYEdhb2N8sGq37BkbYWWqg2L1zEyc3+dTFngi4xtDDNSifTHGVYJvAaioITLBUhajsQTBkIC2MSHowC1yMAhn1yNTOLTAbaHZRPbEgJWH2EebYv0XHcqnoDzkal/decZUXamJHsihtOSUz3S2wECe2t7R8BxEUjBzO5Skpdl/a86NAH1tq6I7apeBB1qHMHtN8qVgHuNtIcqb+7qpMyYxkARgfce3vEL/CpCONHls/KXwkIO5wjD3LYZzQfBKQhIXe6VYKBCHdlPOsPu7LL1mL/ztljNvBDjS15cEZIWjNyKmkwyKulMvSQB7rUdOQFcw5vfSzsDpYhn8dZdN1cF3PSFbWWOXfoaEJQiF1mCci4S59298vzzPU0/XhKw=", "payloadb64": "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"}}