# Filepath: app/routes/diags.py
from flask import Blueprint, request, jsonify, current_app
import time
from random import randrange
import os
from app.models import db, ServerCore
import datetime
import zipfile
import base64
from sqlalchemy import update
from app.models import db, <PERSON><PERSON>Status, <PERSON>ceCollector, <PERSON>ceOSQuery
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_wtf.csrf import CSRFProtect
from app import csrf
from app.utilities.app_logging_helper import log_with_route
import logging

diags_bp = Blueprint('diags_bp', __name__)

# Initialize Flask-Limiter with default rate limits (e.g., 5 requests per minute per IP)
limiter = Limiter(
    get_remote_address,
    app=current_app,
    default_limits=["60 per minute"]
)

@diags_bp.route('/diags/ping', methods=['GET'])
def ping():
    log_with_route(logging.INFO, 'Received ping')
    timeStamp = int(time.time()) + randrange(10)
    log_with_route(logging.INFO, f'Sending pong: {timeStamp}')
    return jsonify({"status": "success", "data": f"pong", "timeStamp": timeStamp}), 200

@diags_bp.route('/diags/echo', methods=['POST'])
def echo():
    log_with_route(logging.INFO, 'Receiving echo')
    try:
        data = request.get_json()
        log_with_route(logging.INFO, f'Sent data: {data}')
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
    return jsonify({"status": "success", "echo": data}), 200

@diags_bp.route('/diags/collectorVersion', methods=['GET'])
@csrf.exempt
def returnCollectorVerion():
    log_with_route(logging.INFO, 'Receiving collectorVersion')
    try:
        serverCore = ServerCore.query.first()
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
    if serverCore:
        serverCollectorVersion = serverCore.collector_version
        serverHashPy = serverCore.collector_hash_py
        serverHashWin = serverCore.collector_hash_win
        log_with_route(logging.INFO, f'serverCollectorVersion: {serverCollectorVersion}')
        return jsonify({"status": "success",
                        "serverCollectorVersion": serverCollectorVersion,
                        "serverHashPy": serverHashPy,
                        "serverHashWin": serverHashWin}), 200
    else:
        log_with_route(logging.ERROR, f'Failed to get serverCollectorVersion')
        return jsonify({"status": "error", "serverCollectorVersion": '0'}), 400

@diags_bp.route('/diags/agentversion', methods=['GET'])
@csrf.exempt
def returnAgentVerion():
    log_with_route(logging.INFO, 'Receiving agentVersion')
    try:
        serverCore = ServerCore.query.first()
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
    if serverCore:
        serverAgentVersion = serverCore.agent_version
        serverAgentHashPy = serverCore.agent_hash_py
        serverAgentHashWin = serverCore.agent_hash_win
        log_with_route(logging.INFO, f'serverAgentVersion: {serverAgentVersion}')
        return jsonify({"status": "success",
                        "serverAgentVersion": serverAgentVersion,
                        "serverAgentHashPy": serverAgentHashPy,
                        "serverAgentHashWin": serverAgentHashWin}), 200
    else:
        log_with_route(logging.ERROR, f'Failed to get serverAgentVersion')
        return jsonify({"status": "error", "serverAgentVersion": '0'}), 400

@diags_bp.route('/diags/persistentagentversion', methods=['GET'])
@csrf.exempt
def returnPersistentAgentVersion():
    log_with_route(logging.INFO, 'Receiving persistentAgentVersion')
    try:
        serverCore = ServerCore.query.first()
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
    if serverCore:
        # Use existing agent fields for persistent agent versioning
        # We can add dedicated fields later if needed
        serverPersistentAgentVersion = getattr(serverCore, 'persistent_agent_version', serverCore.agent_version)
        serverPersistentAgentHashPy = getattr(serverCore, 'persistent_agent_hash_py', serverCore.agent_hash_py)
        log_with_route(logging.INFO, f'serverPersistentAgentVersion: {serverPersistentAgentVersion}')
        return jsonify({"status": "success",
                        "serverPersistentAgentVersion": serverPersistentAgentVersion,
                        "serverPersistentAgentHashPy": serverPersistentAgentHashPy}), 200
    else:
        log_with_route(logging.ERROR, f'Failed to get serverPersistentAgentVersion')
        return jsonify({"status": "error", "serverPersistentAgentVersion": '0'}), 400

@diags_bp.route('/diags/getserverpublickey', methods=['GET'])
@csrf.exempt  # Only use this if you intentionally want to bypass CSRF for this route
def returnServerPublicKey():
    log_with_route(logging.INFO, 'Receiving getserverpublickey')
    try:
        serverCore = ServerCore.query.first()
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
    if serverCore:
        log_with_route(logging.DEBUG, f'serverCore: {serverCore}')
        try:
            serverPubKeyPem = serverCore.server_public_key
            log_with_route(logging.INFO, f'serverPubKeyPem: {serverPubKeyPem}')
        except Exception as e:
            log_with_route(logging.ERROR, f'Failed to get value. Reason: {e}')
        try:
            serverPubKeyPemb64 = base64.b64encode(serverPubKeyPem.encode())
        except Exception as e:
            log_with_route(logging.ERROR, f'failed to b64. Reason: {e}')
        log_with_route(logging.DEBUG, f'serverpublickey (b64): {serverPubKeyPemb64}')
        return jsonify({"status": "success",
                        "serverpublickey": serverPubKeyPemb64.decode('utf-8')}), 200
    else:
        log_with_route(logging.ERROR, f'Failed to get serverPubKeyPem')
        return jsonify({"status": "error", "serverPubKeyPem": '0'}), 400

@diags_bp.route('/diags/archivelogs', methods=['GET'])
@limiter.limit("10 per minute")  # Specific rate limit for this endpoint
def archiveLogs():
    # Restrict access to localhost (127.0.0.1)
    callingAddr = request.headers.getlist("X-Forwarded-For")[0]
    validAddresses = ['127.0.0.1', '**************']
    if callingAddr not in validAddresses:
        log_with_route(logging.ERROR, f'Unauthorized access attempt to /diags/archivelogs/ from |{callingAddr}|.')
        return jsonify({"status": "error", f"called from disallowed address": '0'}), 403

    project_root = os.path.dirname(current_app.root_path)
    logsDir = os.path.join(project_root, 'logs')
    checkDir(logsDir)
    log_with_route(logging.INFO, 'Receiving archivelogs')

    archiveStatus = True
    now = datetime.datetime.now()
    prettyDate = now.strftime('%Y.%m.%d-%H:%M:%S')
    zipName = os.path.join(logsDir, prettyDate) + '.zip'
    with zipfile.ZipFile(zipName, 'w', compression=zipfile.ZIP_DEFLATED) as zipf:
        for logf in os.listdir(logsDir):
            if logf.endswith('.log'):
                log_with_route(logging.DEBUG, f'Found: {os.path.join(logsDir, logf)}')
                log_with_route(logging.INFO, f'Attempting to zip {os.path.join(logsDir, logf)} to {zipName}')
                try:
                    zipf.write(os.path.join(logsDir, logf), f'{prettyDate}_{logf}')
                except Exception as e:
                    log_with_route(logging.ERROR, f'Failed to zip {os.path.join(logsDir, logf)} to {zipName}. Reason: {e}')
                    archiveStatus = False
                log_with_route(logging.INFO, f'Attempting to remove {os.path.join(logsDir, logf)}')
                try:
                    os.remove(os.path.join(logsDir, logf))
                except Exception as e:
                    log_with_route(logging.ERROR, f'Failed to remove {os.path.join(logsDir, logf)}. Reason: {e}')
                    archiveStatus = False
    if archiveStatus == True:
        return jsonify({"status": "success", "data": 'Log archiving complete'}), 200
    else:
        return jsonify({"status": "error", "data": 'Failed to archive logs'}), 500

@diags_bp.route('/diags/testserversigning', methods=['GET'])
@csrf.exempt  # Only use this if you intentionally want to bypass CSRF for this route
def testServerSigning():
    log_with_route(logging.INFO, 'Receiving testserversigning')
    testSignFile = '/home/<USER>/wegweiser/includes/testSignFile.json'
    privateKeyFile = '/home/<USER>/wegweiser/includes/serverPrivKey.pem'
    from cryptography.hazmat.backends import default_backend
    from cryptography.hazmat.primitives.serialization import load_pem_private_key
    from cryptography.hazmat.primitives import hashes
    from cryptography.hazmat.primitives.asymmetric import padding
    import base64
    log_with_route(logging.DEBUG, f'Reading privateKeyFile: {privateKeyFile}')
    with open(privateKeyFile, 'rb') as keyFile:
        privateKey = load_pem_private_key(keyFile.read(), password=None, backend=default_backend())
    log_with_route(logging.DEBUG, f'Reading testSignFile: {testSignFile}')
    with open(testSignFile, 'rb') as f:
        testSignFileBytes = f.read()
    try:
        signature = privateKey.sign(
            testSignFileBytes,
            padding.PKCS1v15(),
            hashes.SHA256()
        )
    except Exception as e:
        log_with_route(logging.ERROR, f'Failed to sign. Reason: {e}')
    b64Sig = base64.b64encode(signature)
    log_with_route(logging.DEBUG, f'signature: {signature} | {type(signature)}')
    log_with_route(logging.DEBUG, f'b64Sig: {b64Sig} | {type(b64Sig)}')
    b64Payload = base64.b64encode(testSignFileBytes)
    log_with_route(logging.DEBUG, f'b64Payload: {b64Payload} | {type(b64Payload)}')

    snippetDict = {}
    snippetDict['payload'] = {}
    snippetDict['payload']['payloadsigb64'] = b64Sig.decode('utf-8')
    snippetDict['payload']['payloadb64'] = b64Payload.decode('utf-8')
    log_with_route(logging.DEBUG, f'snippetDict: {snippetDict}')
    return jsonify({"status": "success", "data": snippetDict}), 200

@diags_bp.route('/diags/checkin/<deviceuuid>', methods=['POST'])
@csrf.exempt
def doCheckin(deviceuuid):
    log_with_route(logging.INFO, f'Receiving checkin for {deviceuuid}')

    try:
        data = request.get_json()
        log_with_route(logging.INFO, f'Received checkin data: {data}')
    except Exception as e:
        log_with_route(logging.ERROR, f'Something went wrong. Reason: {e}')
        return jsonify({"status": "success", "Check in complete": data}), 200

    updateAgentDetailsSql = (
        update(DeviceCollector)
        .where(DeviceCollector.deviceuuid == deviceuuid)
        .values(
            last_update=int(time.time()),
            coll_version=data['agentVersion'],
            coll_install_dir=data['agentInstDir']
        )
    )

    updateLastUpdateSql = (
        update(DeviceStatus)
        .where(DeviceStatus.deviceuuid == deviceuuid)
        .values(
            last_update=int(time.time()),
        )
    )
    try:
        db.session.execute(updateLastUpdateSql)
        db.session.commit()
        log_with_route(logging.INFO, 'Updating DeviceStatus.last_update processed')
    except Exception as e:
        log_with_route(logging.ERROR, f'Error updating DeviceStatus.last_update: Reason: {e}')
        log_with_route(logging.ERROR, f'Rolling back transaction...')
        db.session.rollback()
        log_with_route(logging.ERROR, f'Transaction rolled back.')
        return jsonify({"status": "error", "data": 'Failed to check in(1)'}), 500

    try:
        db.session.execute(updateAgentDetailsSql)
        db.session.commit()
        log_with_route(logging.INFO, 'Updating DeviceCollector.details processed')
    except Exception as e:
        log_with_route(logging.ERROR, f'Error updating DeviceCollector.details: Reason: {e}')
        log_with_route(logging.ERROR, f'Rolling back transaction...')
        db.session.rollback()
        log_with_route(logging.ERROR, f'Transaction rolled back.')
        return jsonify({"status": "error", "data": 'Failed to check in (2)'}), 500
    return jsonify({"status": "success", "data": 'Check-in complete.'}), 200


@diags_bp.route('/diags/osquery/<deviceuuid>', methods=['POST'])
@csrf.exempt
def update_osquery_data(deviceuuid):
    log_with_route(logging.INFO, f'Receiving osquery data for {deviceuuid}')
    
    try:
        data = request.get_json()
        log_with_route(logging.INFO, f'Processing osquery data with sections: {", ".join(data.keys())}')
        
        for query_name, query_data in data.items():
            try:
                DeviceOSQuery.store_query_result(
                    deviceuuid=deviceuuid,
                    query_name=query_name,
                    data=query_data
                )
            except Exception as e:
                log_with_route(logging.ERROR, f'Error storing {query_name}: {str(e)}')
                db.session.rollback()
                continue
        
        return jsonify({"status": "success"}), 200
        
    except Exception as e:
        log_with_route(logging.ERROR, f'Failed to process osquery data: {str(e)}')
        return jsonify({"status": "error", "message": str(e)}), 500


####################### HELPER FUNCTIONS #######################

def checkDir(dirToCheck):
    if os.path.isdir(dirToCheck):
        log_with_route(logging.INFO, f'{dirToCheck} already exists.')
    else:
        log_with_route(logging.INFO, f'{dirToCheck} does not exist. Creating...')
        try:
            os.makedirs(dirToCheck)
            log_with_route(logging.INFO, f'{dirToCheck} created.')
        except Exception as e:
            log_with_route(logging.ERROR, f'Failed to create {dirToCheck}. Reason: {e}')